# 眼动仪校准与验证程序

本项目包含一个使用 Pygame 和 Pylink 库编写的Python脚本，用于对 EyeLink 眼动仪进行校准和验证。

## 文件说明

- `calibration_validation.py`: 主程序脚本。它会执行眼动仪校准，并在之后显示一个验证界面。
- `CalibrationGraphicsPygame.py`: 从 EyeLink 官方示例中提取的辅助文件，用于在校准过程中绘制图形界面。
- `run.bat`: 一个Windows批处理文件，用于方便地启动程序。
- `README.md`: 本说明文件。

## 功能

1.  **动态路径**: 自动检测当前 Python 版本（如3.8, 3.9, 3.11等）和系统架构（32/64位），并设置正确的 `pylink` 库路径。
2.  **眼动仪校准**: 调用 `pylink.doTrackerSetup()` 来启动一个标准的9点校准程序。
3.  **实时验证**: 校准完成后，程序会进入一个验证模式：
    - 屏幕上会显示一个红色的目标点。
    - 屏幕上会实时显示一个绿色的十字，代表当前从眼动仪读取到的注视点位置。
    - 目标点每4秒会随机移动到一个新的位置。
    - 提示文字使用中文显示。
4.  **数据记录**:
    - 自动生成一个8位的随机文件名（如 `A4T9B1C2.EDF`）来保存眼动数据。
    - 验证过程中的目标点切换事件也会被记录到 EDF 文件中。
    - 程序结束后，会自动将 `.EDF` 文件从眼动仪主机下载到本 `gemini` 文件夹内。
5.  **退出**: 在验证界面按 `ESC` 键可以安全退出程序。

## 如何运行

1.  确保您的 EyeLink Host PC 已经开机，并且与实验用电脑通过网线连接。
2.  确保您的 conda 环境 (`eyetracking`) 已经激活，并且安装了 `pygame`。
3.  双击 `run.bat` 文件，或者在终端中进入 `gemini` 目录后，运行 `python calibration_validation.py`。 