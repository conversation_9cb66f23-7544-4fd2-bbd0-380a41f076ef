@echo off
echo ========================================
echo EyeLink校准验证程序启动器
echo ========================================
echo.

echo 激活conda环境...
call conda activate eyetracking

echo.
echo 选择要运行的程序版本：
echo 1. 简化版本（推荐）- simple_calibration_validation.py
echo 2. 完整版本 - calibration_validation.py
echo.

set /p choice="请输入选择 (1 或 2): "

if "%choice%"=="1" (
    echo.
    echo 启动简化版校准验证程序...
    python simple_calibration_validation.py
) else if "%choice%"=="2" (
    echo.
    echo 启动完整版校准验证程序...
    python calibration_validation.py
) else (
    echo.
    echo 无效选择，启动简化版程序...
    python simple_calibration_validation.py
)

echo.
echo 程序已结束。
pause
