"""
本程序用于眼动仪的校准和后续的验证。
实现方式:
1. 使用 Pygame 创建一个窗口。
2. 使用 Pylink 连接到 EyeLink 眼动仪。
3. 调用标准的眼动仪校准程序 (doTrackerSetup)。
4. 校准后，屏幕上会随机出现一个目标点。
5. 程序会实时从眼动仪获取注视数据，并在屏幕上绘制出注视点的位置。
6. 目标点每4秒会切换到新的随机位置。
7. 按下 ESC 键可以退出程序。
"""

import pygame
import pylink
import os
import sys
import random
import time
from pygame.locals import *

# --- 动态添加Pylink路径 ---
# 这会让脚本更具可移植性
major_ver = sys.version_info.major
minor_ver = sys.version_info.minor
arch = '64' if '64bit' in sys.version else '32'
pylink_path = os.path.abspath(os.path.join(
    '..', 
    'eyelink python example', 
    arch, 
    f'{major_ver}.{minor_ver}',
    'pylink'
))

if not os.path.exists(pylink_path):
    print(f"错误: Pylink路径未找到: {pylink_path}")
    print("请确认您的Python版本和架构是否在 'eyelink python example' 文件夹中受支持。")
    sys.exit(1)

print(f"正在使用Pylink路径: {pylink_path}")
sys.path.append(os.path.dirname(pylink_path)) # 添加pylink的上级目录

# 从示例代码中导入校准图形类
# 我们需要将 CalibrationGraphicsPygame.py 复制到 gemini 文件夹下
# 或者修改路径来找到它。为了简单起见，我们稍后会复制它。
from CalibrationGraphicsPygame import CalibrationGraphics

# --- 全局常量 ---
# 屏幕尺寸，设为0, 0则使用pygame全屏检测
SCREEN_RESOLUTION = (1024, 768) 
FULLSCREEN = True # 是否全屏

# EyeLink连接地址, "None" 为虚拟模式
EYELINK_ADDRESS = "*********"

# 中文字体路径 (Windows下的微软雅黑)
# 请根据你的系统修改
FONT_PATH_CHINESE = "C:/Windows/Fonts/msyh.ttf"
# ---

def terminate_task(el_tracker, edf_filename):
    """
    安全地终止任务，关闭连接并下载EDF文件。
    """
    if el_tracker and el_tracker.isConnected():
        # 等待100ms，确保所有数据都写入文件
        pylink.pumpDelay(100)
        
        # 停止记录并关闭文件
        el_tracker.stopRecording(1)
        el_tracker.closeDataFile()

        # 下载EDF文件
        print(f"正在下载EDF文件: {edf_filename}...")
        try:
            el_tracker.receiveDataFile(edf_filename, f"gemini/{edf_filename}")
            print("下载成功。")
        except RuntimeError as e:
            print(f"下载失败: {e}")

        # 关闭与眼动仪的连接
        el_tracker.close()

    # 关闭pygame
    pygame.quit()
    sys.exit()


def main():
    # --- 1. 初始化 Pygame ---
    pygame.init()
    
    # 设置屏幕
    if FULLSCREEN:
        win = pygame.display.set_mode(SCREEN_RESOLUTION, FULLSCREEN | DOUBLEBUF)
    else:
        win = pygame.display.set_mode(SCREEN_RESOLUTION, 0)
    
    scn_width, scn_height = win.get_size()
    pygame.mouse.set_visible(False) # 隐藏鼠标

    # --- 2. 连接到 EyeLink ---
    try:
        if EYELINK_ADDRESS is None:
            el_tracker = pylink.EyeLink(None) # 虚拟模式
        else:
            el_tracker = pylink.EyeLink(EYELINK_ADDRESS)
    except RuntimeError as e:
        print(f"无法连接到眼动仪: {e}")
        pygame.quit()
        sys.exit()

    # --- 3. 设置EDF文件名 (8字符限制) ---
    edf_filename = "".join(random.choice("abcdefghijklmnopqrstuvwxyz12345678") for _ in range(8))
    edf_filename = edf_filename.upper() + ".EDF"

    try:
        el_tracker.openDataFile(edf_filename)
    except RuntimeError as e:
        print(f"无法创建EDF文件 '{edf_filename}': {e}")
        el_tracker.close()
        pygame.quit()
        sys.exit()
    
    # 在EDF文件中写入 preamble text
    preamble_text = f"RECORDED BY calibration_validation.py"
    el_tracker.sendCommand(f"add_file_preamble_text '{preamble_text}'")

    # --- 4. 配置眼动仪参数 ---
    el_tracker.setOfflineMode()
    
    # 获取眼动仪版本
    vstr = el_tracker.getTrackerVersionString()
    eyelink_ver = int(vstr.split()[-1].split('.')[0])
    print(f"眼动仪版本: {vstr}")

    # 文件和链接数据配置
    file_event_flags = 'LEFT,RIGHT,FIXATION,SACCADE,BLINK,MESSAGE,BUTTON,INPUT'
    link_event_flags = 'LEFT,RIGHT,FIXATION,SACCADE,BLINK,BUTTON,FIXUPDATE'
    file_sample_flags = 'LEFT,RIGHT,GAZE,HREF,RAW,AREA,GAZERES,BUTTON,STATUS,INPUT'
    link_sample_flags = 'LEFT,RIGHT,GAZE,GAZERES,AREA,HTARGET,STATUS'
    
    el_tracker.sendCommand(f"file_event_filter = {file_event_flags}")
    el_tracker.sendCommand(f"link_event_filter = {link_event_flags}")
    el_tracker.sendCommand(f"file_sample_data = {file_sample_flags}")
    el_tracker.sendCommand(f"link_sample_data = {link_sample_flags}")

    # 设置屏幕坐标
    el_coords = f"screen_pixel_coords = 0 0 {scn_width - 1} {scn_height - 1}"
    el_tracker.sendCommand(el_coords)
    el_tracker.sendMessage(f"DISPLAY_COORDS 0 0 {scn_width - 1} {scn_height - 1}")

    # --- 5. 设置校准环境 ---
    genv = CalibrationGraphics(el_tracker, win)
    pylink.openGraphicsEx(genv)

    # --- 6. 执行校准 ---
    try:
        el_tracker.doTrackerSetup()
    except RuntimeError as e:
        print(f"校准失败: {e}")
        terminate_task(el_tracker, edf_filename)
        return

    # --- 7. 验证环节 ---
    # 加载中文字体
    try:
        font_chinese = pygame.font.Font(FONT_PATH_CHINESE, 30)
    except IOError:
        print(f"警告: 找不到中文字体 '{FONT_PATH_CHINESE}', 将使用默认字体。")
        font_chinese = pygame.font.Font(None, 35) # 使用默认字体

    # 颜色定义
    background_color = (128, 128, 128)
    target_color = (255, 0, 0) # 红色目标点
    gaze_color = (0, 255, 0)   # 绿色注视点

    # 验证循环
    running = True
    target_change_time = time.time()
    target_pos = (random.randint(50, scn_width-50), random.randint(50, scn_height-50))
    
    # 开始记录
    el_tracker.startRecording(1, 1, 1, 1)
    
    # 确保眼动仪数据是最新的
    pylink.pumpDelay(100) 

    while running:
        # 检查退出事件
        for event in pygame.event.get():
            if event.type == KEYDOWN and event.key == K_ESCAPE:
                running = False

        # 获取最新的眼动数据样本
        sample = el_tracker.getNewestSample()
        gaze_pos = None
        if sample is not None:
            # 对于双眼数据，可以使用平均值或只用一只眼
            if sample.isBinocular():
                gaze_pos = sample.getRightEye().getGaze()
            else:
                gaze_pos = sample.getLeftEye().getGaze()

        # 绘制背景
        win.fill(background_color)

        # 绘制提示文字
        text_surface = font_chinese.render("请注视红色目标点，按ESC退出", True, (0, 0, 0))
        text_rect = text_surface.get_rect(center=(scn_width/2, 50))
        win.blit(text_surface, text_rect)

        # 检查是否需要更换目标点 (每4秒)
        if time.time() - target_change_time > 4.0:
            target_pos = (random.randint(50, scn_width - 50), random.randint(50, scn_height - 50))
            target_change_time = time.time()
            # 发送消息到EDF，记录目标点切换事件
            el_tracker.sendMessage(f"TARGET_UPDATE {target_pos[0]} {target_pos[1]}")

        # 绘制目标点
        pygame.draw.circle(win, target_color, target_pos, 15)
        pygame.draw.circle(win, background_color, target_pos, 7)

        # 绘制注视点
        if gaze_pos and gaze_pos[0] != pylink.MISSING_DATA and gaze_pos[1] != pylink.MISSING_DATA:
            pygame.draw.circle(win, gaze_color, (int(gaze_pos[0]), int(gaze_pos[1])), 10)

        # 更新屏幕
        pygame.display.flip()

    # --- 8. 结束任务 ---
    terminate_task(el_tracker, edf_filename)

if __name__ == '__main__':
    try:
        main()
    except Exception as e:
        print(f"程序出现未处理的异常: {e}")
        pygame.quit()
        sys.exit() 