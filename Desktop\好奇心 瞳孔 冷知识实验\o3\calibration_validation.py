#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
calibration_validation.py

EyeLink 校准 + 实时验证脚本
-------------------------------------------------
功能概述：
1. 连接 EyeLink（支持真实模式 & 虚拟模式）
2. 进行被试校准
3. 校准完成后进入验证阶段：
   - 屏幕随机显示红色光点（约 4 秒切换一次）
   - 实时绘制眼动仪返回的凝视位置（绿色十字）
4. 支持中文提示与 SimHei 字体
5. 自动生成不超过 8 字符的 EDF 文件名
6. 退出后自动接收 EDF 文件并关闭连接

依赖：
- pylink  (SR Research)
- pygame
- 本项目中的 eyelink_manager.EyeLinkManager
- SR Research 官方示例中的 CalibrationGraphicsPygame.py（自动添加到 sys.path）

如何运行：
$ python calibration_validation.py --id test001 --dummy  # 虚拟模式快速测试
$ python calibration_validation.py --id P001            # 真实模式

可用参数：
--id      被试 ID（必填）
--dummy   使用虚拟模式（用于无硬件调试）
--fullscreen  是否全屏（默认 True）
--interval 切换光点间隔秒数（默认 4）
--duration 验证总时长秒（默认 20）

"""

import os
import sys
import time
import random
import argparse
from typing import Tuple

# 第三方库
try:
    import pygame
    from pygame.locals import *
except ImportError as e:
    print("未安装 pygame，请先安装后再运行：pip install pygame")
    raise e

try:
    import pylink
except ImportError:
    # 若未安装 pylink，后续会转入虚拟模式
    pylink = None

# 本地模块
# 为了从子文件夹导入根目录模块，加入父目录到sys.path
BASE_DIR = os.path.dirname(os.path.dirname(__file__))
if BASE_DIR not in sys.path:
    sys.path.insert(0, BASE_DIR)

from eyelink_manager import EyeLinkManager

# ------------------------- 常量配置 ------------------------- #
# 颜色
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
RED   = (255, 0, 0)
GREEN = (0, 255, 0)
GRAY  = (128, 128, 128)

# 光标大小
DOT_SIZE  = 16   # 目标光点半径
GAZE_SIZE = 12   # 凝视位置十字半长

# Chinese font fallback
SIMHEI_PATH = "C:/Windows/Fonts/simhei.ttf"  # Windows 下常见路径

# 路径：CalibrationGraphicsPygame.py
CALIB_PYGAME_PATH = os.path.join(os.path.dirname(__file__),
    "eyelink python example", "examples", "Pygame_examples", "saccade")
if CALIB_PYGAME_PATH not in sys.path:
    sys.path.append(CALIB_PYGAME_PATH)
try:
    from CalibrationGraphicsPygame import CalibrationGraphics  # type: ignore
except Exception as e:
    print("⚠ 无法导入 CalibrationGraphicsPygame：{}".format(e))
    CalibrationGraphics = None  # fallback later


# ------------------------- 工具函数 ------------------------- #

def show_message(win: pygame.Surface, font: pygame.font.Font, message: str,
                 bg_color: Tuple[int, int, int] = GRAY,
                 fg_color: Tuple[int, int, int] = BLACK,
                 wait_key: bool = True,
                 timeout: float = None) -> bool:
    """在屏幕中央显示消息。
    如果 wait_key=True，则等待空格/回车继续；ESC 退出。
    如果 timeout 指定，则显示指定秒数后继续。
    返回值：True=继续，False=退出
    """
    win.fill(bg_color)

    lines = message.split('\n')
    total_height = len(lines) * font.get_linesize()
    start_y = (win.get_height() - total_height) // 2

    for i, line in enumerate(lines):
        if not line.strip():
            continue
        text_surf = font.render(line, True, fg_color)
        text_rect = text_surf.get_rect()
        text_rect.centerx = win.get_width() // 2
        text_rect.y = start_y + i * font.get_linesize()
        win.blit(text_surf, text_rect)

    pygame.display.flip()

    if wait_key:
        waiting = True
        while waiting:
            for event in pygame.event.get():
                if event.type == KEYDOWN:
                    if event.key in (K_SPACE, K_RETURN):
                        waiting = False
                    elif event.key == K_ESCAPE:
                        return False
                elif event.type == QUIT:
                    return False
            pygame.time.wait(16)
    elif timeout is not None:
        time.sleep(timeout)

    return True


def get_font(size: int = 36) -> pygame.font.Font:
    """尝试加载支持中文的字体，失败则退回默认字体"""
    try:
        if os.path.exists(SIMHEI_PATH):
            return pygame.font.Font(SIMHEI_PATH, size)
    except Exception:
        pass
    return pygame.font.SysFont('Arial', size)


def draw_gaze_cross(win: pygame.Surface, pos: Tuple[int, int], color=GREEN):
    """在 pos 处绘制一个绿色十字"""
    x, y = pos
    pygame.draw.line(win, color, (x - GAZE_SIZE, y), (x + GAZE_SIZE, y), 3)
    pygame.draw.line(win, color, (x, y - GAZE_SIZE), (x, y + GAZE_SIZE), 3)


def generate_positions(scn_w: int, scn_h: int, num: int = 9, margin: int = 120):
    """生成屏幕内随机位置列表"""
    positions = []
    for _ in range(num):
        x = random.randint(margin, scn_w - margin)
        y = random.randint(margin, scn_h - margin)
        positions.append((x, y))
    return positions


def get_gaze_position(tracker, eye_used):
    """从 EyeLink tracker 中获取最新 gaze 坐标 (x, y)。若无数据返回 None"""
    if tracker is None:
        return None
    if not tracker.isConnected() or not tracker.isRecording():
        return None
    sample = tracker.getNewestSample()
    if sample is None:
        return None
    if eye_used == 0 and sample.isLeftSample():
        gaze = sample.getLeftEye().getGaze()
    elif eye_used == 1 and sample.isRightSample():
        gaze = sample.getRightEye().getGaze()
    else:
        return None
    return int(gaze[0]), int(gaze[1])


# ------------------------- 主流程 ------------------------- #

def run_validation(args):
    """主入口：连接 -> 校准 -> 验证 -> 关闭"""
    pygame.init()
    pygame.mixer.init(frequency=22050, size=-16, channels=2, buffer=512)

    # 获取显示信息
    info = pygame.display.Info()
    scn_w, scn_h = info.current_w, info.current_h

    flags = pygame.FULLSCREEN if args.fullscreen else 0
    win = pygame.display.set_mode((scn_w, scn_h), flags)
    pygame.display.set_caption("EyeLink 校准验证 Calibration & Validation")

    # 字体
    font_big = get_font(40)
    font_small = get_font(24)

    # 初始化 EyeLinkManager
    data_dir = os.path.join(os.path.dirname(__file__), 'data')
    os.makedirs(data_dir, exist_ok=True)
    eyelink = EyeLinkManager(args.id, data_dir=data_dir, dummy_mode=args.dummy or pylink is None)
    if not eyelink.connect():
        print("连接 EyeLink 失败，程序终止。")
        return

    # 使用真实屏幕尺寸更新 tracker 信息
    eyelink.screen_width = scn_w
    eyelink.screen_height = scn_h

    if not eyelink.setup_tracker():
        print("配置 EyeLink 失败，程序终止。")
        eyelink.close()
        return

    # 打开自定义校准界面
    if CalibrationGraphics:
        genv = CalibrationGraphics(eyelink.tracker, win)
        genv.setTargetSize(24)
        genv.setCalibrationColors((0, 0, 0), (128, 128, 128))
        try:
            pylink.openGraphicsEx(genv)
        except Exception as e:
            print("⚠ openGraphicsEx 失败: {}\n将使用默认校准界面".format(e))
    else:
        try:
            pylink.openGraphics((scn_w, scn_h), 32)
        except Exception as e:
            print("⚠ openGraphics 失败: {}".format(e))

    # ---------------- 校准阶段 ---------------- #
    if not show_message(win, font_big, "按空格开始校准\n\n按 ESC 退出", wait_key=True):
        eyelink.close()
        pygame.quit()
        return

    eyelink.log_calibration_start()
    if not eyelink.calibrate():
        eyelink.log_calibration_end(False)
        eyelink.close()
        pygame.quit()
        return
    eyelink.log_calibration_end(True)

    # ---------------- 验证阶段 ---------------- #
    if not show_message(win, font_big, "校准完成！\n\n接下来屏幕会出现红色光点\n绿色十字为实时眼动位置\n\n按空格开始，ESC 退出", wait_key=True):
        eyelink.close()
        pygame.quit()
        return

    # 开始记录
    eyelink.start_recording(trial_id=0)

    # 进入实时模式以确保快速获取样本（真实模式下）
    if not eyelink.dummy_mode and pylink is not None:
        try:
            pylink.beginRealTimeMode(100)
        except RuntimeError:
            pass  # 如果已在实时模式，可忽略

    # 确定追踪眼
    eye_used = eyelink.tracker.eyeAvailable() if not eyelink.dummy_mode else -1
    if eye_used == 2:  # binocular，默认选择左眼
        eye_used = 0

    # 生成随机位置
    positions = generate_positions(scn_w, scn_h, num=args.num_targets, margin=120)
    position_idx = 0
    last_switch = time.time()
    start_time = time.time()

    running = True
    while running and (time.time() - start_time < args.duration):
        current_time = time.time()
        # 检查切换
        if current_time - last_switch >= args.interval:
            position_idx = (position_idx + 1) % len(positions)
            last_switch = current_time

        # 处理事件
        for event in pygame.event.get():
            if event.type == KEYDOWN and event.key == K_ESCAPE:
                running = False
            elif event.type == QUIT:
                running = False

        # 绘制背景
        win.fill(GRAY)
        # 绘制目标红点
        dot_x, dot_y = positions[position_idx]
        pygame.draw.circle(win, RED, (dot_x, dot_y), DOT_SIZE)

        # 获取 gaze 位置
        if eyelink.dummy_mode:
            # 虚拟模式：模拟 gaze 缓慢跟随目标
            gaze_x, gaze_y = dot_x + random.randint(-40, 40), dot_y + random.randint(-40, 40)
        else:
            gaze_pos = get_gaze_position(eyelink.tracker, eye_used)
            if gaze_pos:
                last_valid_pos = gaze_pos  # 更新最近一次有效凝视
            else:
                gaze_pos = None

            if gaze_pos:
                gaze_x, gaze_y = gaze_pos
            else:
                # 若没有最新样本，则使用上一帧的位置或屏幕中心
                gaze_x, gaze_y = last_valid_pos if 'last_valid_pos' in locals() else (scn_w // 2, scn_h // 2)

        draw_gaze_cross(win, (gaze_x, gaze_y))

        # 绘制剩余时间
        remaining = args.duration - (current_time - start_time)
        time_text = font_small.render(f"剩余: {remaining:.1f}s", True, BLACK)
        win.blit(time_text, (20, 20))

        # 说明文字
        instr = font_small.render("红点: 目标  绿十字: 眼动位置", True, BLACK)
        win.blit(instr, (20, 50))

        pygame.display.flip()
        pygame.time.wait(16)  # ~60 FPS

    # 停止记录并结束
    eyelink.stop_recording()
    show_message(win, font_big, "验证结束！正在保存数据...", wait_key=False, timeout=2)

    eyelink.close()
    pygame.quit()
    print("✅ 校准验证流程完成")


# ------------------------- CLI 入口 ------------------------- #

def parse_args():
    parser = argparse.ArgumentParser(description="EyeLink 校准 + 实时验证脚本")
    parser.add_argument('--id', required=True, help='被试 ID (≤8 字符最前部分将作为 EDF 文件名)')
    parser.add_argument('--dummy', action='store_true', help='使用虚拟模式（无硬件）')
    parser.add_argument('--fullscreen', action='store_true', default=True, help='是否全屏 (默认 True)')
    parser.add_argument('--interval', type=float, default=4.0, help='光点切换间隔秒')
    parser.add_argument('--duration', type=float, default=20.0, help='验证总时长秒')
    parser.add_argument('--num_targets', type=int, default=8, help='随机目标数量')
    return parser.parse_args()


if __name__ == '__main__':
    arguments = parse_args()
    run_validation(arguments) 