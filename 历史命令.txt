我要用psycopy写一些python代码。所有内容在conda 中eyetracking的环境进行。我用eyelink的眼动仪。我会让被试在实验材料库中选择一些题目做随机选3个先，同时记录他的瞳孔和眼动数据。实验流程在单独的txt文档中。现在按照我的实验流程编写一个python程序，有3道随机的题目。写一个todo一个个完成
改进以下内容：1.所有数据存在data文件夹下，命名用日期+时间+id。 2.你需要参考eyelink python example中的pylink api user guide。你需要让eyelink从开始实验后记录瞳孔、眼动、事件数据，并且在结束后返回一个edf文件，具体的事件数据描述当时发生什么，例如“出示题目“1+1=几””，“眼动校准1”，“出示答案”等等。
详细的向我介绍你的每一个python程序的作用 以及其中可以调节的参数。然后修改以下问题：在填空输入的时候，我输入中文每次只能输入一个字，修改成一次可以输入多个。2，填空后只有按回车才换到下一个环节，不是限定时间的。列一个todo一个个完成。
1debug,还是不能连续输入中文（获取文本输入失败: 'TextBox2' object has no attribute 'refresh'）,请仍然用这种对话框，上网搜一下如何键入多个中文；2，改成默认全屏并且使用eyelink机器，默认一道题
单独写一个瞳孔校准的简单的程序，功能是先调用眼动仪的校准功能，然后在屏幕上四个角出现4个点，实时显示我现在的目视点位置。具体接口查询eyelink example中的apiuser，并且上网查询eyelink是如何校准的，为什么要眼动校准，校准什么，瞳孔和目视点位置都要校准吗。列一个todo。
阅读calibrationgraphicspygame.py程序眼动仪校准程序。仿照它，写一个python。先要被试校准，然后在屏幕上随机显示一些光点和实时的眼动目标位置（大概4秒一切换），让我能验证我看的和eyelink认为我看的一样。注意支持中文显示。以及edf文件名要少于8个字符
瞳孔calibration会卡在如图的这一步，attempting measure frame。 debug。你为什么要获取屏幕帧率。我的电脑的帧率是60，然后hdmi给显示屏
瞳孔calibration会卡在如图的这一步 debug。你为什么要获取屏幕帧率。我的电脑的帧率是60，然后hdmi给显示屏
根据pylink api userguide中用psycopy调用一个校准函数，完成校准，然后在屏幕上随机显示光点和我的实时眼动的目标，光点会隔3秒换位置。
你刚才改的很好，可以正常运行。现在把你的calibration validation中的校准加到main_experiemtn中。尽量少修改main experiemt中的其他内容
重新改一下main experiemtn中的选题逻辑，在实验材料库.txt中，前一半是问题，标志是"****************************************问题：共340个**************************************"，然后有答案标志"***********************************答案******************************************"，你需要先获取总问题数，然后根据输入的问题数量随机选择那么多个问题答案对，需要准确的从我的材料库中获取。