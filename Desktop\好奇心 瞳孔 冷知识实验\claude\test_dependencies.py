"""
测试脚本：检查校准验证程序的所有依赖项
目的：确保运行环境配置正确
"""

import sys
import os

def test_basic_imports():
    """测试基本Python模块导入"""
    print("测试基本Python模块...")
    try:
        import random
        import time
        print("✓ 基本Python模块导入成功")
        return True
    except ImportError as e:
        print(f"✗ 基本Python模块导入失败: {e}")
        return False

def test_pygame():
    """测试Pygame模块"""
    print("测试Pygame...")
    try:
        import pygame
        pygame.init()
        print("✓ Pygame导入和初始化成功")
        return True
    except ImportError as e:
        print(f"✗ Pygame导入失败: {e}")
        return False
    except Exception as e:
        print(f"✗ Pygame初始化失败: {e}")
        return False

def test_pylink_path():
    """测试Pylink路径配置"""
    print("测试Pylink路径配置...")
    
    major_ver = sys.version_info.major
    minor_ver = sys.version_info.minor
    arch = '64' if '64bit' in sys.version else '32'
    pylink_path = os.path.abspath(os.path.join(
        '..', 
        'eyelink python example', 
        arch, 
        f'{major_ver}.{minor_ver}',
        'pylink'
    ))
    
    if os.path.exists(pylink_path):
        print(f"✓ Pylink路径存在: {pylink_path}")
        sys.path.append(os.path.dirname(pylink_path))
        return True
    else:
        print(f"✗ Pylink路径不存在: {pylink_path}")
        return False

def test_pylink_import():
    """测试Pylink模块导入"""
    print("测试Pylink模块导入...")
    try:
        import pylink
        print("✓ Pylink模块导入成功")
        return True
    except ImportError as e:
        print(f"✗ Pylink模块导入失败: {e}")
        return False

def test_calibration_graphics():
    """测试CalibrationGraphics类导入"""
    print("测试CalibrationGraphics类导入...")
    try:
        from CalibrationGraphicsPygame import CalibrationGraphics
        print("✓ CalibrationGraphics类导入成功")
        return True
    except ImportError as e:
        print(f"✗ CalibrationGraphics类导入失败: {e}")
        return False

def test_audio_files():
    """测试音频文件存在性"""
    print("测试音频文件...")
    audio_files = ['error.wav', 'qbeep.wav']
    all_found = True
    
    for audio_file in audio_files:
        if os.path.exists(audio_file):
            print(f"✓ 音频文件存在: {audio_file}")
        else:
            print(f"✗ 音频文件不存在: {audio_file}")
            all_found = False
    
    return all_found

def test_font():
    """测试中文字体"""
    print("测试中文字体...")
    font_path = "C:/Windows/Fonts/msyh.ttf"
    
    if os.path.exists(font_path):
        print(f"✓ 中文字体存在: {font_path}")
        return True
    else:
        print(f"⚠ 中文字体不存在: {font_path} (将使用默认字体)")
        return True  # 字体不是必需的，程序会降级到默认字体

def main():
    """主测试函数"""
    print("=" * 50)
    print("开始测试校准验证程序依赖项...")
    print("=" * 50)
    
    tests = [
        test_basic_imports,
        test_pygame,
        test_pylink_path,
        test_pylink_import,
        test_calibration_graphics,
        test_audio_files,
        test_font
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("✓ 所有依赖项测试通过！可以运行校准验证程序。")
    else:
        print("⚠ 部分依赖项测试失败。请检查上述错误。")
    
    print("=" * 50)

if __name__ == '__main__':
    main() 