#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
校准验证程序测试脚本
用于测试程序的基本功能，不需要实际连接EyeLink
"""

import pygame
from pygame.locals import *
import random
import time

class TestCalibrationValidation:
    """测试版校准验证程序（不需要EyeLink）"""
    
    def __init__(self):
        """初始化测试程序"""
        # 初始化pygame
        pygame.init()
        pygame.font.init()
        
        # 设置窗口大小（非全屏，便于测试）
        self.scn_w = 1024
        self.scn_h = 768
        
        # 创建窗口
        self.win = pygame.display.set_mode((self.scn_w, self.scn_h))
        pygame.display.set_caption("校准验证程序测试")
        
        # 设置中文字体
        try:
            self.font = pygame.font.Font("C:/Windows/Fonts/simhei.ttf", 36)
            self.small_font = pygame.font.Font("C:/Windows/Fonts/simhei.ttf", 24)
        except:
            self.font = pygame.font.SysFont('Arial', 36)
            self.small_font = pygame.font.SysFont('Arial', 24)
        
        # 颜色定义
        self.BLACK = (0, 0, 0)
        self.WHITE = (255, 255, 255)
        self.RED = (255, 0, 0)
        self.GREEN = (0, 255, 0)
        self.BLUE = (0, 0, 255)
        self.GRAY = (128, 128, 128)
        
        # 验证参数
        self.dot_size = 20
        self.gaze_size = 15
        self.switch_interval = 2.0  # 测试时缩短间隔
        self.validation_duration = 10.0  # 测试时缩短时长
        
        # 模拟眼动位置
        self.simulated_gaze = (self.scn_w // 2, self.scn_h // 2)
        
    def show_message(self, message, duration=None, wait_key=False):
        """显示消息"""
        self.win.fill(self.GRAY)
        
        # 分行显示消息
        lines = message.split('\n')
        total_height = len(lines) * 50
        start_y = (self.scn_h - total_height) // 2
        
        for i, line in enumerate(lines):
            if line.strip():
                text_surface = self.font.render(line, True, self.BLACK)
                text_rect = text_surface.get_rect()
                text_rect.centerx = self.scn_w // 2
                text_rect.y = start_y + i * 50
                self.win.blit(text_surface, text_rect)
        
        pygame.display.flip()
        
        if wait_key:
            waiting = True
            while waiting:
                for event in pygame.event.get():
                    if event.type == KEYDOWN:
                        if event.key == K_SPACE or event.key == K_RETURN:
                            waiting = False
                        elif event.key == K_ESCAPE:
                            return False
                    elif event.type == QUIT:
                        return False
        elif duration:
            time.sleep(duration)
        
        return True
    
    def simulate_gaze_position(self, target_pos):
        """模拟眼动位置（向目标位置移动）"""
        target_x, target_y = target_pos
        current_x, current_y = self.simulated_gaze
        
        # 添加一些随机噪声
        noise_x = random.randint(-20, 20)
        noise_y = random.randint(-20, 20)
        
        # 向目标位置移动（模拟眼动延迟）
        new_x = current_x + (target_x - current_x) * 0.3 + noise_x
        new_y = current_y + (target_y - current_y) * 0.3 + noise_y
        
        self.simulated_gaze = (int(new_x), int(new_y))
        return self.simulated_gaze
    
    def generate_random_positions(self, num_positions=6):
        """生成随机光点位置"""
        positions = []
        margin = 100
        
        for _ in range(num_positions):
            x = random.randint(margin, self.scn_w - margin)
            y = random.randint(margin, self.scn_h - margin)
            positions.append((x, y))
        
        return positions
    
    def run_test_validation(self):
        """运行测试验证"""
        try:
            # 显示测试说明
            if not self.show_message("校准验证测试\n\n这是一个测试版本\n不需要连接EyeLink\n\n按空格键开始", wait_key=True):
                return False
            
            # 生成随机位置
            positions = self.generate_random_positions()
            
            start_time = time.time()
            position_index = 0
            last_switch_time = start_time
            
            print("开始验证测试...")
            
            # 验证循环
            while time.time() - start_time < self.validation_duration:
                current_time = time.time()
                
                # 检查是否需要切换位置
                if current_time - last_switch_time >= self.switch_interval:
                    position_index = (position_index + 1) % len(positions)
                    last_switch_time = current_time
                    
                    dot_x, dot_y = positions[position_index]
                    print(f"光点位置切换到: ({dot_x}, {dot_y})")
                
                # 清空屏幕
                self.win.fill(self.GRAY)
                
                # 绘制当前光点
                dot_x, dot_y = positions[position_index]
                pygame.draw.circle(self.win, self.RED, (dot_x, dot_y), self.dot_size)
                
                # 模拟并绘制眼动位置
                gaze_pos = self.simulate_gaze_position((dot_x, dot_y))
                gaze_x, gaze_y = gaze_pos
                
                # 绘制眼动位置（绿色十字）
                pygame.draw.line(self.win, self.GREEN, 
                               (gaze_x - self.gaze_size, gaze_y), 
                               (gaze_x + self.gaze_size, gaze_y), 3)
                pygame.draw.line(self.win, self.GREEN, 
                               (gaze_x, gaze_y - self.gaze_size), 
                               (gaze_x, gaze_y + self.gaze_size), 3)
                
                # 显示剩余时间
                remaining_time = self.validation_duration - (current_time - start_time)
                time_text = f"剩余时间: {remaining_time:.1f}秒"
                time_surface = self.small_font.render(time_text, True, self.BLACK)
                self.win.blit(time_surface, (20, 20))
                
                # 显示说明
                instruction_text = "红点: 目标位置  绿十字: 模拟眼动位置"
                instruction_surface = self.small_font.render(instruction_text, True, self.BLACK)
                self.win.blit(instruction_surface, (20, 50))
                
                # 显示测试信息
                test_text = "这是测试模式 - 绿十字会自动跟随红点"
                test_surface = self.small_font.render(test_text, True, self.BLUE)
                self.win.blit(test_surface, (20, 80))
                
                pygame.display.flip()
                
                # 检查退出事件
                for event in pygame.event.get():
                    if event.type == KEYDOWN:
                        if event.key == K_ESCAPE:
                            return False
                    elif event.type == QUIT:
                        return False
                
                # 控制帧率
                pygame.time.wait(16)
            
            # 显示完成消息
            self.show_message("测试完成！\n\n程序功能正常", duration=2)
            print("验证测试完成！")
            
            return True
            
        except Exception as e:
            print(f"测试过程出错: {e}")
            return False
    
    def run(self):
        """运行测试程序"""
        try:
            print("启动校准验证程序测试...")
            
            # 运行测试验证
            success = self.run_test_validation()
            
            if success:
                print("测试成功完成！")
            else:
                print("测试被中断。")
            
            return success
            
        except Exception as e:
            print(f"测试程序出错: {e}")
            return False
        
        finally:
            pygame.quit()


def main():
    """主函数"""
    print("=" * 50)
    print("校准验证程序功能测试")
    print("=" * 50)
    print("这个测试程序会模拟校准验证的基本功能")
    print("不需要连接EyeLink眼动仪")
    print()
    
    # 创建测试程序
    test_validation = TestCalibrationValidation()
    
    # 运行测试
    success = test_validation.run()
    
    if success:
        print("\n✅ 测试通过！校准验证程序功能正常。")
    else:
        print("\n❌ 测试失败或被中断。")


if __name__ == "__main__":
    main()
